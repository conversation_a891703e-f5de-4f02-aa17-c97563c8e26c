import 'dart:async';

// import 'dart:io';
import 'package:cbrs/core/common/widgets/text_scale_wrapper.dart';
import 'package:cbrs/core/localization/controllers/language_controller.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/services/connectivity/connectivity_controller.dart';
import 'package:cbrs/core/services/device/app_life_cycle_observer.dart';
import 'package:cbrs/core/services/firebase_notification_service/fcm_service.dart';
import 'package:cbrs/core/services/injection_container.dart' as di;
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route.dart';
import 'package:cbrs/core/utils/get_app_theme_controller.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/cash_in_out/application/bloc/cash_in_cash_out_bloc.dart';
import 'package:cbrs/features/change_to_birr/application/bloc/change_to_birr_bloc.dart';
import 'package:cbrs/features/exchange_rate/application/bloc/exchange_rate_bloc/exchange_rate_bloc.dart';
import 'package:cbrs/features/exchange_rate/application/bloc/exchange_rate_bloc/exchange_rate_event.dart';
import 'package:cbrs/features/exchange_rate/domain/usecases/get_exchange_rates_usecase.dart';
import 'package:cbrs/features/gift_packages/application/bloc/packages_bloc.dart';
import 'package:cbrs/features/gift_packages/domain/repositories/gift_package_repository.dart';
import 'package:cbrs/features/gift_packages/domain/usecases/confirm_gift_package_payment_usecase.dart';
import 'package:cbrs/features/gift_packages/domain/usecases/get_banner_packages.dart';
import 'package:cbrs/features/gift_packages/domain/usecases/get_gift_packages.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:cbrs/features/home/<USER>/bloc/wallet_balance_bloc.dart';
import 'package:cbrs/features/in_app_update/application_layer/controller/app_update_bloc.dart';
import 'package:cbrs/features/link_acccount/application_layer/bloc/link_account_bloc.dart';
import 'package:cbrs/features/link_acccount/presentation/views/index.dart';
import 'package:cbrs/features/load_wallet/application/bloc/load_wallet_bloc.dart';
import 'package:cbrs/features/load_wallet/domain/usecases/check_load_wallet_status_usecase.dart';
import 'package:cbrs/features/load_wallet/domain/usecases/load_to_wallet_usecase.dart';
import 'package:cbrs/features/merchant_payment/application/bloc/merchant_bloc.dart';
import 'package:cbrs/features/merchant_payment/application/bloc/merchant_payment_bloc.dart';
import 'package:cbrs/features/money_request/presentation/bloc/money_request_detail/money_request_detail_bloc.dart';
import 'package:cbrs/features/notifications/application/notification_bloc/notification_bloc.dart';
import 'package:cbrs/features/orders/application/blocs/order_bloc.dart';
import 'package:cbrs/features/orders/domain/repositories/order_repository.dart';
import 'package:cbrs/features/send_money/application/bloc/bank_transfer_bloc.dart';
import 'package:cbrs/features/top_up/applications/top_up_bloc.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/check_rule_transfer_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_recent_transaction_bloc.dart';
import 'package:cbrs/firebase_options.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:cbrs/features/pay_by_qr/application/bloc/parse_qr_bloc.dart';
import 'package:leak_tracker/leak_tracker.dart';
import 'package:cbrs/features/my_connect/applications/bloc/my_connect_bloc.dart';
import 'package:cbrs/core/services/device/device_security_service.dart';
import 'package:cbrs/core/services/device/compromised_device_page.dart';

@pragma('vm:entry-point')
final GlobalKey<NavigatorState> mainNavigatorKey = GlobalKey<NavigatorState>();

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
}

Future<String?> _getSavedLanguageCode() async {
  const storage = FlutterSecureStorage();
  return storage.read(key: 'language_code');
}

void main() async {
  final widgetsBinding = WidgetsFlutterBinding.ensureInitialized();

  // Device security check before any other initialization
  // final deviceSecurityService = DeviceSecurityService();
  // final compromiseType = await deviceSecurityService.getCompromiseType();
  // if (compromiseType != CompromiseType.none) {
  //   runApp(
  //     MaterialApp(
  //       home: CompromisedDevicePage(compromiseType: compromiseType),
  //       debugShowCheckedModeBanner: false,
  //     ),
  //   );
  //   return;
  // }

  LeakTracking.start();

  final themeController = Get.put(GetAppThemeController());

  final savedLanguageCode = await _getSavedLanguageCode();

  // If there is no saved language, default to 'en'
  final defaultLocale = savedLanguageCode != null
      ? Locale(savedLanguageCode)
      : const Locale('en');

  final flutterI18nDelegate = FlutterI18nDelegate(
    translationLoader: NamespaceFileTranslationLoader(
      namespaces: ['common', 'home'],
      basePath: 'assets/i18n_namespace',
      forcedLocale: defaultLocale,
    ),
    missingTranslationHandler: (key, locale) {},
  );

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Enable Firestore offline persistence
  FirebaseFirestore.instance.settings = const Settings(
    persistenceEnabled: true,
    cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
  );
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);

  final appDocumentDir = await getApplicationDocumentsDirectory();
  await Hive.initFlutter(appDocumentDir.path);

  // Initialize HydratedBloc storage
  HydratedBloc.storage = await HydratedStorage.build(
    storageDirectory: appDocumentDir,
  );

  await Hive.openBox<dynamic>('settingsBox');

  await init();

  Get.put(LanguageController());
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

  await sl<FcmService>().storeFcmToken();

  runApp(MyApp(flutterI18nDelegate));

  FlutterNativeSplash.remove();
}

Future<bool> isEmulator() async {
  final deviceInfoPlugin = DeviceInfoPlugin();

  final androidDeviceInfo = await deviceInfoPlugin.androidInfo;

  if (androidDeviceInfo.isPhysicalDevice == false) {
    return true;
  }
  return false;
}

class MyApp extends StatelessWidget {
  MyApp(this.flutterI18nDelegate, {super.key});
  final FlutterI18nDelegate flutterI18nDelegate;
  final themeController = Get.find<GetAppThemeController>();
  @override
  Widget build(BuildContext context) {
    final settingsBox = Hive.box<dynamic>('settingsBox');
    final savedLanguageCode =
        settingsBox.get('languageCode', defaultValue: 'en') as String;

    return AppLifecycleObserver(
      child: MultiBlocProvider(
        providers: [
          BlocProvider<BankTransferBloc>(
            create: (context) => sl<BankTransferBloc>(),
          ),
          BlocProvider<ParseQrBloc>(
            create: (context) => sl<ParseQrBloc>(),
          ),
          BlocProvider<RecentWalletTransferBloc>(
            create: (context) => sl<RecentWalletTransferBloc>(),
          ),
          BlocProvider<LoadWalletBloc>(
            create: (context) => LoadWalletBloc(
              loadToWalletUseCase: di.sl<LoadToWalletUseCase>(),
              checkLoadWalletStatusUseCase:
                  di.sl<CheckLoadWalletStatusUseCase>(),
            ),
          ),
          BlocProvider(
            create: (context) => ExchangeRateBloc(
              getExchangeRatesUsecase: sl<GetExchangeRatesUsecase>(),
            )..add(FetchExchangeRates()),
          ),
          BlocProvider<ChangeToBirrBloc>(
            create: (context) => di.sl<ChangeToBirrBloc>(),
          ),
          RepositoryProvider<GiftPackageRepository>(
            create: (context) => sl<GiftPackageRepository>(),
          ),
          BlocProvider<CheckRuleTransferBloc>(
            create: (context) => sl<CheckRuleTransferBloc>(),
          ),
          BlocProvider(
            create: (context) => PackagesBloc(
              getGiftPackages: sl<GetGiftPackages>(),
              getBannerPackages: sl<GetBannerPackages>(),
              confirmGiftPackagePayment: sl<ConfirmGiftPackagePaymentUsecase>(),
            ),
          ),
          Provider<AuthLocalDataSource>(
            create: (context) => sl<AuthLocalDataSource>(),
          ),
          Provider<Dio>(
            create: (context) => sl<Dio>(),
          ),
          BlocProvider(
            create: (context) => OrderBloc(
              repository: sl<OrderRepository>(),
            ),
          ),
          BlocProvider(
            create: (context) => sl<NotificationBloc>(),
          ),
          BlocProvider(
            create: (context) => sl<WalletBalanceBloc>(),
          ),
          BlocProvider<MyConnectBloc>(
            create: (context) => sl<MyConnectBloc>(),
          ),
          BlocProvider(
            create: (context) => sl<HomeBloc>(),
          ),
          BlocProvider<MerchantBloc>(
            create: (context) => sl<MerchantBloc>(),
          ),
          BlocProvider<MerchantPaymentBloc>(
            create: (context) => sl<MerchantPaymentBloc>(),
          ),
          BlocProvider<CashInCashOutBloc>(
            create: (context) => CashInCashOutBloc(
              generateVoucherUseCase: sl(),
              searchAgentUseCase: sl(),
              generateBillUseCase: sl(),
              checkTransferRulesUseCase: sl(),
              confirmPaymentUseCase: sl(),
              verifyOtpUseCase: sl(),
              resendOtpUseCase: sl(),
              parseAgentQrUseCase: sl(),
            ),
          ),
          BlocProvider(
            create: (_) => sl<AccountLinkBloc>(),
          ),
          BlocProvider(
            create: (_) => sl<TopUpBloc>(),
          ),
          BlocProvider(
            create: (_) => sl<WalletTransferBloc>(),
          ),
          BlocProvider(
            create: (_) => sl<MoneyRequestDetailBloc>(),
          ),
          BlocProvider(
            create: (_) => sl<TransactionBloc>(),
          ),
          BlocProvider(
            create: (_) => sl<InAppUpdateBloc>(),
          ),
        ],
        child: TextScaleWrapper(
          child: ScreenUtilInit(
            designSize: const Size(400, 932),
            minTextAdapt: true,
            splitScreenMode: true,
            builder: (_, __) => GestureDetector(
              onTap: () {
                FocusScope.of(context).unfocus();
              },
              child: GetMaterialApp.router(
                title: 'CONNECT',
                debugShowCheckedModeBanner: false,
                // showPerformanceOverlay: true,
                theme: themeController.isBirrTheme.value
                    ? LightModeTheme().themeData
                    : LightModeTheme().dollarThemeData,
                localizationsDelegates: [
                  flutterI18nDelegate,
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                ],
                themeMode: ThemeMode.light,
                routeInformationParser: router.routeInformationParser,
                routerDelegate: router.routerDelegate,
                routeInformationProvider: router.routeInformationProvider,
                initialBinding: BindingsBuilder<void>(() {
                  Get.put<ConnectivityController>(
                    sl<ConnectivityController>(),
                    permanent: true,
                  );
                }),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
