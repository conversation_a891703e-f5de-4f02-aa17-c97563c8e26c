import 'dart:async';

import 'package:cbrs/core/common/widgets/success/custom_success_transaction_bottom_sheet.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/guest/application/bloc/guest_bloc.dart';
import 'package:cbrs/features/guest/domain/entities/guest_transfer_status.dart';
import 'package:cbrs/features/guest/presentation/views/guest_transfer_failure_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:webview_flutter/webview_flutter.dart';

class GuestWebViewScreen extends StatefulWidget {
  const GuestWebViewScreen({
    required this.url,
    required this.redirectURL,
    required this.billRefNo,
    super.key,
  });
  final String url;
  final String redirectURL;
  final String billRefNo;

  @override
  State<GuestWebViewScreen> createState() => _GuestWebViewScreenState();
}

class _GuestWebViewScreenState extends State<GuestWebViewScreen> {
  Timer? _statusCheckTimer;
  late final WebViewController _controller;
  bool _hasError = false;
  int _retryCount = 0;
  static const int _maxRetries = 3;
  bool _isLoading = true;

  final ignoredErrors = [
    'net::ERR_CONNECTION_TIMED_OUT',
    'net::ERR_CONNECTION_RESET',
    'net::ERR_SSL_PROTOCOL_ERROR',
  ];

  @override
  void initState() {
    super.initState();
    _initWebView();
  }

  void _initWebView() {
    if (widget.url.isEmpty) {
      setState(() => _hasError = true);
      return;
    }

    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..enableZoom(false)
      ..setBackgroundColor(Colors.white)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (url) {
            if (mounted) {
              setState(() {
                _isLoading = true;
                _hasError = false;
              });
            }
            _checkUrlChange(url);
          },
          onPageFinished: (url) {
            if (mounted) {
              setState(() => _isLoading = false);
            }
            _checkUrlChange(url);
          },
          onWebResourceError: _onWebResourceError,
          onNavigationRequest: (request) {
            _checkUrlChange(request.url);
            return NavigationDecision.navigate;
          },
        ),
      );

    _loadWebView();
  }

  void _loadWebView() {
    try {
      _controller.loadRequest(
        Uri.parse(widget.url),
        headers: {
          'Content-Security-Policy':
              "frame-ancestors 'self' https://mtf.gateway.mastercard.com/",
          'X-Frame-Options': 'ALLOW-FROM https://mtf.gateway.mastercard.com/',
        },
      );
    } catch (e) {
      debugPrint('Error loading WebView: $e');
      if (mounted) {
        setState(() => _hasError = true);
      }
    }
  }

  void _startStatusCheck() {
    _statusCheckTimer?.cancel();
    var attemptCount = 0;
    const maxAttempts = 30; // 5 minutes with 10-second intervals

    _statusCheckTimer = Timer.periodic(
      const Duration(seconds: 10),
      (timer) {
        if (!mounted) {
          timer.cancel();
          return;
        }

        attemptCount++;
        if (attemptCount >= maxAttempts) {
          timer.cancel();
          if (mounted) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder: (_) => BlocProvider.value(
                  value: context.read<GuestBloc>(),
                  child: const GuestFailureScreen(),
                ),
              ),
            );
          }
          return;
        }

        debugPrint(
          'Checking transfer status (attempt $attemptCount/$maxAttempts)',
        );
        context.read<GuestBloc>().add(
              CheckGuestTransferStatus(billRefNo: widget.billRefNo),
            );
      },
    );
  }

  void _checkUrlChange(String currentUrl) {
    debugPrint('✅Current URL: $currentUrl');

    // Check if URL contains loading.html and resultIndicator
    if (currentUrl.contains('loading.html') &&
        currentUrl.contains('resultIndicator')) {
      // Extract resultIndicator from URL if needed
      final uri = Uri.parse(currentUrl);
      final resultIndicator = uri.queryParameters['resultIndicator'];

      debugPrint('✅Payment completed, resultIndicator: $resultIndicator');

      // Start final status check with shorter timeout
      _startFinalStatusCheck();
    }
  }

  void _startFinalStatusCheck() {
    _statusCheckTimer?.cancel();
    var attemptCount = 0;
    const maxAttempts = 6; // 30 seconds with 5-second intervals

    _statusCheckTimer = Timer.periodic(
      const Duration(seconds: 5),
      (timer) {
        if (!mounted) {
          timer.cancel();
          return;
        }

        attemptCount++;
        debugPrint('Final status check attempt $attemptCount/$maxAttempts');

        if (attemptCount >= maxAttempts) {
          timer.cancel();
          if (mounted) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder: (_) => BlocProvider.value(
                  value: context.read<GuestBloc>(),
                  child: const GuestFailureScreen(),
                ),
              ),
            );
          }
          return;
        }

        context.read<GuestBloc>().add(
              CheckGuestTransferStatus(billRefNo: widget.billRefNo),
            );
      },
    );
  }

  void _onWebResourceError(WebResourceError error) {
    debugPrint('Web resource error: ${error.description}');

    if (mounted && !_isLoading) {
      setState(() {
        _hasError = true;
        _retryCount++;
      });

      // If max retries reached, show failure screen
      if (_retryCount >= _maxRetries) {
        _statusCheckTimer?.cancel();
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (_) => BlocProvider.value(
              value: context.read<GuestBloc>(),
              child: const GuestFailureScreen(),
            ),
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _statusCheckTimer?.cancel();
    _controller
      ..clearCache()
      ..clearLocalStorage();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<GuestBloc, GuestState>(
      listenWhen: (previous, current) {
        // Also listen for loading state to show/hide loader
        return current is TransferSuccess ||
            current is GuestError ||
            current is GuestLoading;
      },
      listener: (context, state) {
        if (state is TransferSuccess) {
          _statusCheckTimer?.cancel();
          debugPrint('✅Transfer status: ${state.transaction}');

          // Wrap in try-catch for safer navigation
          try {
            // context.goNamed(
            //   AppRouteName.guestTransferSuccess,
            //   extra: {'transferStatus': state.transaction},
            // );
            _showSuccessBottomSheet(state.transaction);
          } catch (e) {
            // context.goNamed(AppRouteName.guestHomePage);
          }
        } else if (state is GuestError) {
          _statusCheckTimer?.cancel();

          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (_) => BlocProvider.value(
                value: context.read<GuestBloc>(),
                child: const GuestFailureScreen(),
              ),
            ),
          );

          // context.goNamed(AppRouteName.guestFailure);
        }
      },
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              'Payment',
              style: GoogleFonts.outfit(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          body: SafeArea(
            child: Stack(
              children: [
                if (!_hasError) WebViewWidget(controller: _controller),
                if (_isLoading || state is GuestLoading)
                  const Center(
                    child: CircularProgressIndicator(),
                  ),
                if (_hasError &&
                    _retryCount <
                        _maxRetries) // Only show error if retries available
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Text('Failed to load payment page'),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadWebView,
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showSuccessBottomSheet(GuestTransactionStatus transaction) {
    final blocValue = context.read<GuestBloc>();

    final data = {
      'Transaction Type': 'Guest Bank Transfer',
      'Sender Name': transaction.senderName,
      'Recipient Name': transaction.beneficiaryName,
      'Recipeint Account': transaction.beneficiaryAccountNo,
      'Bank Name': transaction.bankName,
      'Amount in USD': '${transaction.billAmount} USD',
      'Exchange Rate': '1\$ = ${transaction.exchangeRate} ETB',
      'Amount In ETB': '${transaction.paidAmount} ETB',
      'Service fee': "${transaction.serviceCharge} USD",
      'VAT': "${transaction.vat}",
      'Date': AppMapper.safeFormattedDate(DateTime.now()),
      'Connect Ref No.': "${transaction.walletFTNumber}",
      'FT Reference.': "${transaction.ftNumber}"
    };

    showModalBottomSheet<dynamic>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: false,
      enableDrag: false,
      builder: (context) {
        return BlocProvider.value(
          value: blocValue,
          child: CustomSuccessTransactionBottomSheet(
            data: data,
            transactionType: 'Guest Send Money',
            transactionId: transaction.id,
            billAmount: transaction.billAmount,
            totalAmount: transaction.totalAmount,
            billRefNo: transaction.billRefNo,
            originalCurrency: 'USD',
            isGuestMode: true,
            onContinue: () {},
          ),
        );
      },
    );
  }
}
