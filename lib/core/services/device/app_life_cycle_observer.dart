import 'package:cbrs/core/services/device/device_security_service.dart';
import 'package:cbrs/core/services/device/session_timeout_service.dart';
import 'package:cbrs/core/services/heartbeat_service.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/navigation/navigation_service.dart';
import 'package:cbrs/core/services/presence_service.dart';
import 'package:cbrs/features/in_app_update/application_layer/controller/in_app_update_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AppLifecycleObserver extends StatefulWidget {
  const AppLifecycleObserver({
    required this.child,
    super.key,
  });
  final Widget child;

  @override
  State<AppLifecycleObserver> createState() => _AppLifecycleObserverState();
}

class _AppLifecycleObserverState extends State<AppLifecycleObserver>
    with WidgetsBindingObserver {
  late final SessionTimeoutService _sessionTimeoutService;
  late final PresenceService _presenceService;
  late final HeartbeatService _heartbeatService;
  bool _wasInBackground = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _sessionTimeoutService = Get.find<SessionTimeoutService>();
    _presenceService = sl<PresenceService>();
    _heartbeatService = sl<HeartbeatService>();

    // 🔐 Perform security check on app launch
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   DeviceSecurityService().validateDevice(context);
    // });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Handle presence updates (only if initialized)
    if (_presenceService.isInitialized) {
      _presenceService.handleAppLifecycleChange(state);
    }

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        _wasInBackground = true;
        _sessionTimeoutService.startBackgroundTimer();
        // Stop heartbeat when app goes to background
        _heartbeatService.stopAllHeartbeats();
      case AppLifecycleState.resumed:
        if (_wasInBackground) {
          // Ensure the context is ready before checking timeout
          WidgetsBinding.instance.addPostFrameCallback((_) async {
            final didTimeout =
                await _sessionTimeoutService.checkAndHandleTimeout();
            if (!didTimeout) {
              _sessionTimeoutService.stopBackgroundTimer();
            }
          });
          _wasInBackground = false;
        }
      // Note: Heartbeat will be restarted by individual features when needed
      case AppLifecycleState.detached:
        _sessionTimeoutService.stopBackgroundTimer();
        _heartbeatService.stopAllHeartbeats();
        Get.find<NavigationService>().resetInitialLaunch();
      case AppLifecycleState.hidden:
        // Handle hidden state for newer Flutter versions
        break;
    }
  }

  @override
  void dispose() {
    // _securityCheckTimer?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => widget.child;
}
