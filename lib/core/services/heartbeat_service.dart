import 'dart:async';

import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

/// Service to maintain authentication token freshness through periodic heartbeat calls
///
/// This service prevents token expiration during extended user sessions like:
/// - Chat conversations
/// - ComplyCube photo capture sessions
/// - Other long-running user interactions
class HeartbeatService extends GetxService {
  final ApiService _apiService;
  final AuthLocalDataSource _authLocalDataSource;
  final Connectivity _connectivity = Connectivity();

  // Configuration
  static const Duration _defaultHeartbeatInterval = Duration(minutes: 3);
  static const Duration _retryDelay = Duration(seconds: 30);
  static const int _maxRetryAttempts = 3;

  // State management
  Timer? _heartbeatTimer;
  bool _isActive = false;
  bool _isRetrying = false;
  int _retryCount = 0;
  DateTime? _lastSuccessfulHeartbeat;

  // Feature tracking
  final Set<HeartbeatFeature> _activeFeatures = <HeartbeatFeature>{};

  HeartbeatService({
    required ApiService apiService,
    required AuthLocalDataSource authLocalDataSource,
  })  : _apiService = apiService,
        _authLocalDataSource = authLocalDataSource;

  /// Check if heartbeat service is currently active
  bool get isActive => _isActive;

  /// Get the last successful heartbeat timestamp
  DateTime? get lastHeartbeat => _lastSuccessfulHeartbeat;

  /// Get currently active features
  Set<HeartbeatFeature> get activeFeatures => Set.unmodifiable(_activeFeatures);

  /// Start heartbeat for a specific feature
  ///
  /// [feature] - The feature requesting heartbeat (chat, complyCube, etc.)
  /// [interval] - Custom heartbeat interval (optional, defaults to 3 minutes)
  Future<void> startHeartbeat(
    HeartbeatFeature feature, {
    Duration? interval,
  }) async {
    debugPrint('🫀 HeartbeatService: Starting heartbeat for ${feature.name}');

    _activeFeatures.add(feature);

    // If already active, just add the feature and continue
    if (_isActive) {
      debugPrint('🫀 HeartbeatService: Already active, added ${feature.name}');
      return;
    }

    // Check if user is authenticated
    final token = await _authLocalDataSource.getAuthToken();
    if (token == null) {
      debugPrint(
          '🫀 HeartbeatService: No auth token found, cannot start heartbeat');
      return;
    }

    // Check network connectivity
    if (!await _isConnected()) {
      debugPrint(
          '🫀 HeartbeatService: No network connection, deferring heartbeat start');
      return;
    }

    _isActive = true;
    _retryCount = 0;

    final heartbeatInterval = interval ?? _defaultHeartbeatInterval;

    // Start the periodic heartbeat timer
    _heartbeatTimer =
        Timer.periodic(heartbeatInterval, (_) => _performHeartbeat());

    // Perform initial heartbeat immediately
    await _performHeartbeat();

    debugPrint(
        '🫀 HeartbeatService: Started with ${heartbeatInterval.inMinutes}min interval');
  }

  /// Stop heartbeat for a specific feature
  ///
  /// [feature] - The feature no longer needing heartbeat
  void stopHeartbeat(HeartbeatFeature feature) {
    debugPrint('🫀 HeartbeatService: Stopping heartbeat for ${feature.name}');

    _activeFeatures.remove(feature);

    // If no features are active, stop the service
    if (_activeFeatures.isEmpty) {
      _stopHeartbeatService();
    } else {
      debugPrint(
          '🫀 HeartbeatService: Still active for: ${_activeFeatures.map((f) => f.name).join(', ')}');
    }
  }

  /// Force stop all heartbeat activity
  void stopAllHeartbeats() {
    debugPrint('🫀 HeartbeatService: Force stopping all heartbeats');
    _activeFeatures.clear();
    _stopHeartbeatService();
  }

  /// Internal method to stop the heartbeat service
  void _stopHeartbeatService() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
    _isActive = false;
    _isRetrying = false;
    _retryCount = 0;
    debugPrint('🫀 HeartbeatService: Service stopped');
  }

  /// Perform a single heartbeat by refreshing the auth token
  Future<void> _performHeartbeat() async {
    if (!_isActive) return;

    try {
      debugPrint(
          '🫀 HeartbeatService: Performing heartbeat via token refresh...');

      // Check network connectivity before making the call
      if (!await _isConnected()) {
        debugPrint(
            '🫀 HeartbeatService: No network connection, skipping heartbeat');
        return;
      }

      final result = await _apiService.post<Map<String, dynamic>>(
        ApiEndpoints.fetchLinkedAccount,
        parser: (data) => data as Map<String, dynamic>,
      );

      result.fold(
        (data) {
          // Success - token was refreshed
          _lastSuccessfulHeartbeat = DateTime.now();
          _retryCount = 0;
          _isRetrying = false;
          debugPrint(
              '🫀 HeartbeatService: ✅ Token refreshed successfully at $_lastSuccessfulHeartbeat');

          // Save the new token if provided
          final token = data['token'];
          if (token != null && token is String) {
            _authLocalDataSource.saveAuthToken(token);
            debugPrint('🫀 HeartbeatService: New token saved');
          }
        },
        (error) {
          // Error
          debugPrint(
              '🫀 HeartbeatService: ❌ Token refresh failed: ${error.message}');
          _handleHeartbeatError(error);
        },
      );
    } catch (e) {
      debugPrint('🫀 HeartbeatService: ❌ Heartbeat exception: $e');
      _handleHeartbeatError(null);
    }
  }

  /// Handle heartbeat errors with retry logic
  void _handleHeartbeatError(dynamic error) {
    _retryCount++;

    if (_retryCount >= _maxRetryAttempts) {
      debugPrint(
          '🫀 HeartbeatService: Max retry attempts reached, stopping service');
      _stopHeartbeatService();
      return;
    }

    if (!_isRetrying) {
      _isRetrying = true;
      debugPrint(
          '🫀 HeartbeatService: Scheduling retry $_retryCount/$_maxRetryAttempts in ${_retryDelay.inSeconds}s');

      Timer(_retryDelay, () {
        _isRetrying = false;
        if (_isActive) {
          _performHeartbeat();
        }
      });
    }
  }

  /// Check network connectivity
  Future<bool> _isConnected() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    } catch (e) {
      debugPrint('🫀 HeartbeatService: Connectivity check failed: $e');
      return false;
    }
  }

  @override
  void onClose() {
    _stopHeartbeatService();
    super.onClose();
  }
}

/// Enum representing different features that can request heartbeat
enum HeartbeatFeature {
  chat('Chat'),
  complyCube('ComplyCube'),
  general('General');

  const HeartbeatFeature(this.displayName);
  final String displayName;
}
